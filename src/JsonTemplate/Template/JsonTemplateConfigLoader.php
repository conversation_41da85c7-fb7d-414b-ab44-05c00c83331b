<?php

declare(strict_types=1);

namespace App\JsonTemplate\Template;

use App\JsonTemplate\Template\ConfigCache\JsonTemplateConfigCacheFactoryInterface;
use App\JsonTemplate\Template\Locator\JsonTemplateLocation;
use Twig\Environment;
use Visymo\Serializer\NativeJsonArraySerializer;

final class JsonTemplateConfigLoader
{
    /** @var array<string, mixed[]> */
    private array $memoryCache = [];

    public function __construct(
        private readonly Environment $twig,
        private readonly NativeJsonArraySerializer $nativeJsonArraySerializer,
        private readonly JsonTemplateConfigCacheFactoryInterface $jsonTemplateConfigCacheFactory,
        private readonly JsonTemplateResolver $jsonTemplateResolver,
        private readonly bool $allowTemplateCache
    )
    {
    }

    /**
     * @return mixed[]
     */
    public function load(JsonTemplateLocation $jsonTemplateLocation): array
    {
        $cacheKey = $jsonTemplateLocation->templateFilePath;

        // Check memory cache first
        if (isset($this->memoryCache[$cacheKey])) {
            return $this->memoryCache[$cacheKey];
        }

        $configCache = null;

        if ($this->allowTemplateCache) {
            $configCache = $this->jsonTemplateConfigCacheFactory->create(
                $jsonTemplateLocation->templateFilePath,
            );

            if ($configCache->isFresh()) {
                $config = $configCache->read();
                $this->memoryCache[$cacheKey] = $config;
                return $config;
            }
        }

        $jsonTemplateData = $this->twig->render($jsonTemplateLocation->templateFile);
        $config = $this->jsonTemplateResolver->resolve(
            $this->nativeJsonArraySerializer->deserialize($jsonTemplateData),
        );
        $configCache?->write($config);

        // Store in memory cache
        $this->memoryCache[$cacheKey] = $config;

        return $config;
    }
}
