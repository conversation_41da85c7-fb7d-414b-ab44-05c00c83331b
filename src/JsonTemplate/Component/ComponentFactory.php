<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component;

final class ComponentFactory
{
    private int $componentId = 0;

    /** @var array<string, string> */
    private array $namespaceCache = [];

    /** @var array<string, ComponentFactoryInterface> */
    private array $factoryCache = [];

    public function __construct(
        private readonly ComponentNamespaceMapper $componentNamespaceMapper,
        private readonly ComponentFactoryLocator $componentFactoryLocator
    )
    {
    }

    /**
     * @param mixed[] $options
     *
     * @return ComponentInterface[]
     */
    public function createMultipleFromOptions(array $options): array
    {
        if (empty($options)) {
            return [];
        }

        // Pre-warm caches for all component types in batch
        $componentTypes = [];
        foreach ($options as $componentOptions) {
            $componentType = $componentOptions[ComponentInterface::KEY_TYPE];
            if (!isset($this->namespaceCache[$componentType])) {
                $componentTypes[] = $componentType;
            }
        }

        // Batch load namespaces and factories
        foreach ($componentTypes as $componentType) {
            if (!isset($this->namespaceCache[$componentType])) {
                $this->namespaceCache[$componentType] = $this->componentNamespaceMapper->getNamespace($componentType);
            }
            $componentNamespace = $this->namespaceCache[$componentType];
            if (!isset($this->factoryCache[$componentNamespace])) {
                $this->factoryCache[$componentNamespace] = $this->componentFactoryLocator->getComponentFactory($componentNamespace);
            }
        }

        // Create components using cached factories
        $components = [];
        foreach ($options as $componentOptions) {
            $components[] = $this->createFromOptions($componentOptions);
        }

        return $components;
    }

    public function generateId(): int
    {
        return $this->componentId++;
    }

    /**
     * @param mixed[] $options
     */
    public function createFromOptions(array $options): ComponentInterface
    {
        $componentType = $options[ComponentInterface::KEY_TYPE];

        // Use cached namespace if available
        if (!isset($this->namespaceCache[$componentType])) {
            $this->namespaceCache[$componentType] = $this->componentNamespaceMapper->getNamespace($componentType);
        }
        $componentNamespace = $this->namespaceCache[$componentType];

        // Use cached factory if available
        if (!isset($this->factoryCache[$componentNamespace])) {
            $this->factoryCache[$componentNamespace] = $this->componentFactoryLocator->getComponentFactory($componentNamespace);
        }
        $componentFactory = $this->factoryCache[$componentNamespace];

        return $componentFactory->create($options, $this);
    }

    /**
     * @param mixed[]|null $options
     */
    public function createNullableFromOptions(?array $options): ?ComponentInterface
    {
        return $options !== null
            ? $this->createFromOptions($options)
            : null;
    }
}
