<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component;

use Symfony\Component\DependencyInjection\ServiceLocator;

class ComponentRendererLocator
{
    /** @var array<string, ComponentRendererInterface> */
    private array $rendererCache = [];

    /**
     * @param ServiceLocator<mixed> $serviceLocator
     */
    public function __construct(private readonly ServiceLocator $serviceLocator)
    {
    }

    public function getRenderer(ComponentInterface $component): ComponentRendererInterface
    {
        $rendererClass = $component->getRenderer();

        if (!isset($this->rendererCache[$rendererClass])) {
            $this->rendererCache[$rendererClass] = $this->serviceLocator->get($rendererClass);
        }

        return $this->rendererCache[$rendererClass];
    }
}
