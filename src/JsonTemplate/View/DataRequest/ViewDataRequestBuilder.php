<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\DataRequest;

use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\ComponentRendererLocator;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\ViewInterface;

class ViewDataRequestBuilder
{
    public function __construct(
        protected readonly ComponentRendererLocator $componentRendererLocator
    )
    {
    }

    public function buildForView(ViewInterface $view): void
    {
        $this->buildForComponents(
            components: $view->getComponents(),
            view      : $view,
            conditions: new ViewDataConditionCollection(),
        );
    }

    /**
     * @param ComponentInterface[] $components
     */
    public function buildForComponents(
        array $components,
        ViewInterface $view,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (empty($components)) {
            return;
        }

        // Pre-warm renderer cache for all components
        foreach ($components as $component) {
            $this->componentRendererLocator->getRenderer($component);
        }

        // Build components with cached renderers
        foreach ($components as $component) {
            $this->componentRendererLocator
                ->getRenderer($component)
                ->build(
                    component : $component,
                    view      : $view,
                    conditions: $conditions,
                );
        }
    }
}
