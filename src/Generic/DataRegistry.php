<?php

declare(strict_types=1);

namespace App\Generic;

use App\JsonTemplate\View\Data\ViewDataProperty;

final class DataRegistry
{
    /** @var array<string, mixed> */
    private array $properties = [];

    /** @var array<string, mixed> */
    private array $memoizedResults = [];

    public function has(ViewDataProperty $property): bool
    {
        return array_key_exists($property->value, $this->properties);
    }

    public function get(ViewDataProperty $property): mixed
    {
        if (!$this->has($property)) {
            throw new \RuntimeException(
                sprintf(
                    'The property "%s" is not available in the current data registry',
                    $property->value,
                ),
            );
        }

        $value = $this->properties[$property->value];

        // If it's a callable (lazy loading), memoize the result
        if (is_callable($value)) {
            $cacheKey = $property->value . '_memoized';
            if (!isset($this->memoizedResults[$cacheKey])) {
                $this->memoizedResults[$cacheKey] = $value;
            }
            return $this->memoizedResults[$cacheKey];
        }

        return $value;
    }

    public function set(ViewDataProperty $property, mixed $value): self
    {
        $this->properties[$property->value] = $value;

        return $this;
    }
}
